/**
 * This file includes polyfills needed by <PERSON>ular and is loaded before the app.
 * You can add your own extra polyfills to this file.
 *
 * This file is divided into 2 sections:
 *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.
 *   2. Application imports. Files imported after ZoneJS that should be loaded before your main
 *      file.
 *
 * The current setup is for so-called "evergreen" browsers; the last versions of browsers that
 * automatically update themselves. This includes Safari >= 10, Chrome >= 55 (including Opera),
 * Edge >= 13 on the desktop, and iOS 10 and Chrome on mobile.
 *
 * Learn more in https://angular.io/guide/browser-support
 */

/***************************************************************************************************
 * BROWSER POLYFILLS
 */

/** IE10 and IE11 requires the following for NgClass support on SVG elements */
// import 'classlist.js';  // Run `npm install --save classlist.js`.

/**
 * Web Animations `@angular/animations`
 * Only required if AnimationBuilder is used within the application and using IE/Edge or Safari.
 * Standard animation support in Angular DOES NOT require any polyfills (as of Angular 6.0).
 */
// import 'web-animations-js';  // Run `npm install --save web-animations-js`.

/**
 * By default, zone.js will patch all possible macroTask and DomEvents
 * user can disable parts of macroTask/DomEvents patch by setting following flags
 * because those flags need to be set before `zone.js` being loaded, and webpack
 * will put import in the top of bundle, so user need to create a separate file
 * in this directory (for example: zone-flags.ts), and put the following flags
 * into that file, and then add the following code before importing zone.js.
 * import './zone-flags';
 *
 * The flags allowed in zone-flags.ts are listed here.
 *
 * The following flags will disable zone.js completely:
 *
 * (window as any).__Zone_disable_requestAnimationFrame = true; // disable patch requestAnimationFrame
 * (window as any).__Zone_disable_on_property = true; // disable patch onProperty such as onclick
 * (window as any).__Zone_disable_geolocation = true; // disable patch geolocation
 * (window as any).__Zone_disable_file = true; // disable patch file
 * (window as any).__Zone_disable_fs = true; // disable patch fs
 * (window as any).__Zone_disable_canvas = true; // disable patch canvas
 * (window as any).__Zone_disable_scroll = true; // disable patch scroll
 * (window as any).__Zone_disable_resize = true; // disable patch resize
 * (window as any).__Zone_disable_unhandledRejection = true; // disable patch unhandledRejection
 * (window as any).__Zone_disable_toString = true; // disable patch toString
 * (window as any).__Zone_disable_blocking = true; // disable patch blocking
 * (window as any).__Zone_disable_EventTarget = true; // disable patch EventTarget
 * (window as any).__Zone_disable_MutationObserver = true; // disable patch MutationObserver
 * (window as any).__Zone_disable_IntersectionObserver = true; // disable patch IntersectionObserver
 * (window as any).__Zone_disable_on_property = true; // disable patch onProperty such as onclick
 * (window as any).__Zone_disable_customElements = true; // disable patch customElements
 *
 */

/***************************************************************************************************
 * Zone JS is required by default for Angular itself.
 */
// import 'zone.js/dist/zone';  // Included with Angular CLI.

/***************************************************************************************************
 * APPLICATION IMPORTS
 */
