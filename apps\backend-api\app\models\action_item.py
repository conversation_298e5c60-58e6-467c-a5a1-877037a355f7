from sqlalchemy import Column, String, Text, Boolean, Foreign<PERSON>ey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .base import TenantBaseModel


class ActionItem(TenantBaseModel):
    """Action items for human-in-the-loop workflow"""
    __tablename__ = "action_items"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=True)
    agent_session_id = Column(UUID(as_uuid=True), ForeignKey("agent_sessions.id"), nullable=True)
    session_id = Column(UUID(as_uuid=True), ForeignKey("sessions.id"), nullable=True)
    
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    priority = Column(String(20), nullable=False, default="medium")  # low, medium, high, urgent
    category = Column(String(50), nullable=False, default="review")  # review, validation, error, manual_entry
    
    # Status
    is_completed = Column(Boolean, default=False, nullable=False)
    completed_by = Column(UUID(as_uuid=True), nullable=True)  # User ID who completed
    resolution_notes = Column(Text, nullable=True)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="action_items")
    user = relationship("User", back_populates="action_items")
    invoice = relationship("Invoice", back_populates="action_items")
    session = relationship("Session", back_populates="action_items")

    def __repr__(self):
        return f"<ActionItem(id={self.id}, title='{self.title}', completed={self.is_completed})>"
